import { useEffect, useState } from "react";
import { useSet<PERSON>tom } from "jotai";

import { supabase } from "@/integrations/supabase/client";
// FIX 1: Component names should be PascalCase (starts with a capital letter)
import { MobileReactionButton } from "./MobileReactionButton";
import { acceptedCraftmanCountAtom } from "@/states/job";

interface JobResponsesProps {
  jobId: string;
  status: string;
  isOwner?: boolean;
  handleComplete?: (hired_craftman_id: string) => Promise<void>;
}

export const JobResponses = ({
  jobId,
  status,
  isOwner = false,
  handleComplete,
}: JobResponsesProps) => {
  const setAcceptedCraftmanCount = useSetAtom(acceptedCraftmanCountAtom);

  const [responses, setResponses] = useState<any[]>([]);
  const [hasResponded, setHasResponded] = useState(false);
  const [showResponses, setShowResponses] = useState(false);

  useEffect(() => {
    const fetchResponses = async () => {
      try {
        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) return;

        const { data: userResponse } = await supabase
          .from("job_responses")
          .select("id")
          .match({ job_id: jobId, vakman_id: user.id })
          .maybeSingle();

        setHasResponded(Boolean(userResponse));

        const statusFilters = {
          completed: "accepted",
          in_behandeling: "accepted",
          accepted: "accepted",
          open: "pending",
        };

        const baseQuery = {
          job_id: jobId,
          ...(statusFilters[status as keyof typeof statusFilters] && {
            status: statusFilters[status as keyof typeof statusFilters],
          }),
          ...(isOwner ? {} : { vakman_id: user.id }),
        };

        const { data: jobResponses, error } = await supabase
          .from("job_responses")
          .select(
            `
            id,
            status,
            message,
            created_at,
            profiles:vakman_id (
              id,
              first_name,
              last_name,
              email,
              phone_number,
              profile_photo_url,
              company_name,
              kvk_number,
              btw_number
            )
          `
          )
          .match(baseQuery)
          .order("created_at");

        if (error) throw error;

        const responses = jobResponses || [];
        setResponses(responses);

        const acceptedCount = responses.filter(
          (response) => response.status === "accepted"
        ).length;
        setAcceptedCraftmanCount(acceptedCount);
      } catch (error) {
        console.error("Error fetching responses:", error);
      }
    };

    fetchResponses();
  }, [jobId, status, isOwner]);

  if (!responses.length && !hasResponded) {
    return null;
  }

  // FIX 2 & 3: Corrected the switch statement
  const getTitle = () => {
    switch (status) {
      case "completed":
        return "Mission réalisée par";
      case "open":
        return hasResponded
          ? "Vous avez répondu à cette mission"
          : "Réponses des artisans";
      case "in_behandeling": // Fixed typo from "in"
      case "accepted":
        return "Artisan choisi";
      default:
        return "";
    }
  }; // Removed extra closing brace

  return (
    <>
      <div className={`space-y-4 ${!showResponses ? "hidden md:block" : ""}`}>
        <h2 className="text-lg font-semibold">{getTitle()}</h2>
        <div className="space-y-4">
          {responses.map((response) => (
            // FIX 1 (cont.): Use the PascalCase component name
            <VakmanProfileCard
              key={response.id}
              profile={response.profiles}
              jobId={jobId}
              showAcceptButton={isOwner && response.status === "pending"}
              status={status}
              isOwner={isOwner}
              handleComplete={handleComplete}
              isAccepted={response.status === "accepted"}
            />
          ))}
        </div>
      </div>

      {responses.length > 0 && (
        <MobileReactionButton
          count={responses.length}
          onClick={() => setShowResponses(!showResponses)}
          isShow={showResponses}
        />
      )}
    </>
  );
};