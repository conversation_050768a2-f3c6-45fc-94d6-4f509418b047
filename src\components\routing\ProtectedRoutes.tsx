import { useEffect, useState } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAtom } from "jotai";
import { Loader2 } from "lucide-react";

import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Header } from "@/components/layout/Header";
import { PublicLayout } from "@/components/layout/PublicLayout";
import AuthMFA from "@/components/security/AuthMFA";
import { useAuth } from "@/components/auth/hooks/useAuth";
import { showMFAScreenAtom } from "@/states/admin";

import {
  isPublicRoute,
  isLandingRoute,
  ROUTE_PATHS,
  USER_TYPES,
  MFA_CONFIG,
  TOAST_MESSAGES,
} from "@/config/routes";

import {
  LandingRouteHandler,
  PublicRouteHandler,
  ProtectedRouteHandler,
} from "./RouteHandler";

interface ProtectedRoutesProps {
  session: any;
}

export const ProtectedRoutes = ({ session }: ProtectedRoutesProps) => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const location = useLocation();
  const [showMFAScreen, setShowMFAScreen] = useAtom(showMFAScreenAtom);
  const [readyToShow, setReadyToShow] = useState(false);

  const isKlusaanvrager = userProfile?.user_type === USER_TYPES.KLUSAANVRAGER;
  const currentIsPublicRoute = isPublicRoute(location.pathname);
  const currentIsLandingRoute = isLandingRoute(location.pathname);
  const isAdminRoute = (pathname: string) =>
    pathname.startsWith(ROUTE_PATHS.ADMIN);

  useEffect(() => {
    const checkMFAStatus = async () => {
      try {
        const { data, error } =
          await supabase.auth.mfa.getAuthenticatorAssuranceLevel();

        if (error) throw error;

        const requiresMFAUpgrade =
          data.nextLevel === MFA_CONFIG.REQUIRED_LEVEL &&
          data.nextLevel !== data.currentLevel;
        setShowMFAScreen(requiresMFAUpgrade);
      } catch (error) {
        console.error("MFA check failed:", error);
      } finally {
        setReadyToShow(true);
      }
    };

    checkMFAStatus();
  }, [userProfile?.user_type, setShowMFAScreen]);

  // Handle landing pages
  if (currentIsLandingRoute) {
    return <LandingRouteHandler pathname={location.pathname} />;
  }

  // Handle public routes
  if (currentIsPublicRoute) {
    return (
      <PublicLayout>
        <PublicRouteHandler />
      </PublicLayout>
    );
  }

  // For non-public routes, check authentication
  if (!session) {
    return <Navigate to={ROUTE_PATHS.AUTH} replace />;
  }

  // Prevent unauthorized users from accessing admin routes
  if (
    isAdminRoute(location.pathname) &&
    userProfile &&
    userProfile.user_type !== USER_TYPES.ADMIN
  ) {
    toast({
      variant: "destructive",
      title: "Geen toegang",
      description: "Je hebt geen toegang tot het beheerdersgedeelte.",
    });
    return <Navigate to={ROUTE_PATHS.HOME} replace />;
  }

  if (!readyToShow) return <></>;

  if (showMFAScreen) {
    return <AuthMFA />;
  }

  // Prevent vakmans from accessing /jobs/new
  if (
    userProfile?.user_type === USER_TYPES.VAKMAN &&
    location.pathname === ROUTE_PATHS.JOBS_NEW
  ) {
    toast({
      variant: "destructive",
      ...TOAST_MESSAGES.ACCESS_DENIED,
    });
    return <Navigate to={ROUTE_PATHS.HOME} replace />;
  }

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Header
        onLogout={async () => await supabase.auth.signOut()}
        isKlusaanvrager={isKlusaanvrager}
      />
      <main className="flex-1">
        {userProfile ? (
          <ProtectedRouteHandler />
        ) : (
          <div className="h-[calc(100vh-85px)] w-full flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        )}
      </main>
    </div>
  );
};
