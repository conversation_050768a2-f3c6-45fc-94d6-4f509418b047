import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useUnreadMessages } from "@/hooks/useUnreadMessages";
import { DashboardItems } from "./dashboard/DashboardItems";
import { DashboardAlert } from "./dashboard/DashboardAlert";
import { useAuth } from "./auth/hooks/useAuth";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";

export const VakmanDashboard = () => {
  const navigate = useNavigate();
  const { userProfile } = useAuth();
  const [reviews, setReviews] = useState<any[]>([]);
  const [averageRating, setAverageRating] = useState<number | null>(null);
  const [showProfileDialog, setShowProfileDialog] = useState(false);

  const { toast } = useToast();
  const {
    unreadMessageCount,
    unreadResponseCount,
    resetUnreadMessageCount,
    resetUnreadResponseCount,
  } = useUnreadMessages(userProfile.id || "");

  useEffect(() => {
    const fetchReviews = async () => {
      try {
        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) return;

        const { data: reviewsData, error } = await supabase
          .from("vakman_reviews")
          .select(
            `
            *,
            job:jobs(title),
            reviewer:profiles!vakman_reviews_reviewer_id_fkey(
              first_name,
              last_name
            )
          `
          )
          .eq("vakman_id", user.id)
          .order("created_at", { ascending: false });

        if (error) {
          console.error("Error fetching reviews:", error);
          toast({
            variant: "destructive",
            title: "Fout bij ophalen beoordelingen",
            description:
              "Er is een fout opgetreden bij het ophalen van je beoordelingen.",
          });
        } else if (reviewsData) {
          setReviews(reviewsData);

          const totalRating = reviewsData.reduce(
            (sum, review) => sum + review.rating,
            0
          );
          const avgRating =
            reviewsData.length > 0 ? totalRating / reviewsData.length : null;
          setAverageRating(avgRating);
        }
      } catch (error) {
        console.error("Error in fetchReviews:", error);
      }
    };

    fetchReviews();
  }, [toast]);

  const handleCardClick = async (section: string) => {
    switch (section) {
      case "my-responses":
        console.log("Navigating to my responses section");
        navigate("/mijn_antwoorden");
        resetUnreadResponseCount();
        break;
      case "portfolio":
        console.log("Navigating to portfolio section");
        navigate("/portefeuille");
        break;
      case "available-jobs":
        console.log("Navigating to available jobs section");
        navigate("/banen");
        break;
      case "balance":
        console.log("Navigating to balance section");
        navigate("/evenwicht");
        break;
      case "reviews":
        console.log("Navigating to reviews section");
        navigate("/beoordelingen");
        break;
      case "chat":
        console.log("Navigating to chats section");
        navigate("/gesprekken");
        resetUnreadMessageCount();
        break;
      case "bonus":
        navigate("/bonus");
        break;
      default:
        break;
    }
  };

  const profileDialog = (
    <Dialog open={showProfileDialog} onOpenChange={setShowProfileDialog}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Profiel voltooien vereist</DialogTitle>
          <DialogDescription className="space-y-3 pt-3">
            <p>
              Om beschikbare opdrachten te kunnen zien, moet je eerst je profiel
              voltooien met je KvK- en BTW-nummer.
            </p>
            <div className="flex justify-end space-x-3 pt-4">
              <button
                className="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md"
                onClick={() => setShowProfileDialog(false)}
              >
                Annuleren
              </button>
              <button
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md"
                onClick={() => {
                  setShowProfileDialog(false);
                  navigate("/profiel");
                }}
              >
                Naar profiel
              </button>
            </div>
          </DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="min-h-[calc(100vh-85px)] py-12">
      <div className="container mx-auto p-6 max-w-7xl">
        <div className="mb-8 animate-fade-in">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Welkom, {userProfile.first_name || "Vakman"}
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Wat wil je vandaag doen?
          </p>
        </div>

        <DashboardAlert profile={userProfile} />

        <DashboardItems
          profile={userProfile}
          balance={userProfile.balance}
          reviews={reviews}
          averageRating={averageRating}
          unreadMessageCount={unreadMessageCount}
          unreadResponseCount={unreadResponseCount}
          onCardClick={handleCardClick}
        />
      </div>
      {profileDialog}
    </div>
  );
};
