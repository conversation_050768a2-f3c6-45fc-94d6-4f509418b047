export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      admin_logs: {
        Row: {
          action: string
          admin_id: string
          created_at: string
          details: Json
          id: string
        }
        Insert: {
          action: string
          admin_id: string
          created_at?: string
          details: Json
          id?: string
        }
        Update: {
          action?: string
          admin_id?: string
          created_at?: string
          details?: Json
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "admin_logs_admin_id_fkey"
            columns: ["admin_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      admin_settings: {
        Row: {
          created_at: string
          id: string
          key: string
          updated_at: string
          value: Json
        }
        Insert: {
          created_at?: string
          id?: string
          key: string
          updated_at?: string
          value: Json
        }
        Update: {
          created_at?: string
          id?: string
          key?: string
          updated_at?: string
          value?: Json
        }
        Relationships: []
      }
      balance_transactions: {
        Row: {
          amount: number
          completed_at: string | null
          created_at: string
          description: string | null
          id: string
          invoice_url: string | null
          mollie_payment_id: string | null
          pdf_url: string | null
          status: string | null
          type: string
          user_id: string
        }
        Insert: {
          amount: number
          completed_at?: string | null
          created_at?: string
          description?: string | null
          id?: string
          invoice_url?: string | null
          mollie_payment_id?: string | null
          pdf_url?: string | null
          status?: string | null
          type: string
          user_id: string
        }
        Update: {
          amount?: number
          completed_at?: string | null
          created_at?: string
          description?: string | null
          id?: string
          invoice_url?: string | null
          mollie_payment_id?: string | null
          pdf_url?: string | null
          status?: string | null
          type?: string
          user_id?: string
        }
        Relationships: []
      }
      email_logs: {
        Row: {
          email_type: string
          error: string | null
          id: string
          sent_at: string | null
          status: string | null
          user_id: string
        }
        Insert: {
          email_type: string
          error?: string | null
          id?: string
          sent_at?: string | null
          status?: string | null
          user_id: string
        }
        Update: {
          email_type?: string
          error?: string | null
          id?: string
          sent_at?: string | null
          status?: string | null
          user_id?: string
        }
        Relationships: []
      }
      job_responses: {
        Row: {
          created_at: string
          id: string
          job_id: string
          message: string | null
          response_time_minutes: number | null
          status: string
          updated_at: string
          vakman_id: string
          viewed_at: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          job_id: string
          message?: string | null
          response_time_minutes?: number | null
          status?: string
          updated_at?: string
          vakman_id: string
          viewed_at?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          job_id?: string
          message?: string | null
          response_time_minutes?: number | null
          status?: string
          updated_at?: string
          vakman_id?: string
          viewed_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "job_responses_job_id_fkey"
            columns: ["job_id"]
            isOneToOne: false
            referencedRelation: "jobs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "job_responses_vakman_id_fkey"
            columns: ["vakman_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      jobs: {
        Row: {
          budget: number | null
          created_at: string
          deleted_at: string | null
          description: string
          details: Json | null
          house_number: string
          house_number_addition: string | null
          id: string
          photos: Json
          postal_code: string
          response_cost: number | null
          status: string | null
          title: string
          user_id: string
        }
        Insert: {
          budget?: number | null
          created_at?: string
          deleted_at?: string | null
          description: string
          details?: Json | null
          house_number: string
          house_number_addition?: string | null
          id?: string
          photos?: Json
          postal_code: string
          response_cost?: number | null
          status?: string | null
          title: string
          user_id: string
        }
        Update: {
          budget?: number | null
          created_at?: string
          deleted_at?: string | null
          description?: string
          details?: Json | null
          house_number?: string
          house_number_addition?: string | null
          id?: string
          photos?: Json
          postal_code?: string
          response_cost?: number | null
          status?: string | null
          title?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "jobs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      messages: {
        Row: {
          attachment_type: string | null
          attachment_url: string | null
          content: string
          created_at: string
          id: string
          job_id: string
          read: boolean | null
          receiver_id: string
          sender_id: string
        }
        Insert: {
          attachment_type?: string | null
          attachment_url?: string | null
          content: string
          created_at?: string
          id?: string
          job_id: string
          read?: boolean | null
          receiver_id: string
          sender_id: string
        }
        Update: {
          attachment_type?: string | null
          attachment_url?: string | null
          content?: string
          created_at?: string
          id?: string
          job_id?: string
          read?: boolean | null
          receiver_id?: string
          sender_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "messages_job_id_fkey"
            columns: ["job_id"]
            isOneToOne: false
            referencedRelation: "jobs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_receiver_id_fkey"
            columns: ["receiver_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      portfolio_projects: {
        Row: {
          budget: number | null
          created_at: string
          description: string | null
          id: string
          photos: Json | null
          title: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          budget?: number | null
          created_at?: string
          description?: string | null
          id?: string
          photos?: Json | null
          title: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          budget?: number | null
          created_at?: string
          description?: string | null
          id?: string
          photos?: Json | null
          title: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "portfolio_projects_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          balance: number | null
          btw_number: string | null
          company_name: string | null
          created_at: string
          email: string | null
          first_name: string | null
          full_name: string | null
          house_number: string | null
          house_number_addition: string | null
          id: string
          kvk_number: string | null
          last_name: string | null
          phone_number: string | null
          profile_photo_url: string | null
          street_address: string | null
          updated_at: string
          user_type: string | null
        }
        Insert: {
          balance?: number | null
          btw_number?: string | null
          company_name?: string | null
          created_at?: string
          email?: string | null
          first_name?: string | null
          full_name?: string | null
          house_number?: string | null
          house_number_addition?: string | null
          id: string
          kvk_number?: string | null
          last_name?: string | null
          phone_number?: string | null
          profile_photo_url?: string | null
          street_address?: string | null
          updated_at?: string
          user_type?: string | null
        }
        Update: {
          balance?: number | null
          btw_number?: string | null
          company_name?: string | null
          created_at?: string
          email?: string | null
          first_name?: string | null
          full_name?: string | null
          house_number?: string | null
          house_number_addition?: string | null
          id?: string
          kvk_number?: string | null
          last_name?: string | null
          phone_number?: string | null
          profile_photo_url?: string | null
          street_address?: string | null
          updated_at?: string
          user_type?: string | null
        }
        Relationships: []
      }
      referrals: {
        Row: {
          bonus_paid: boolean | null
          completed_at: string | null
          created_at: string
          id: string
          referred_email: string
          referred_user_id: string | null
          referrer_id: string
          status: string
        }
        Insert: {
          bonus_paid?: boolean | null
          completed_at?: string | null
          created_at?: string
          id?: string
          referred_email: string
          referred_user_id?: string | null
          referrer_id: string
          status: string
        }
        Update: {
          bonus_paid?: boolean | null
          completed_at?: string | null
          created_at?: string
          id?: string
          referred_email?: string
          referred_user_id?: string | null
          referrer_id?: string
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "referrals_referred_user_id_fkey"
            columns: ["referred_user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "referrals_referrer_id_fkey"
            columns: ["referrer_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      review_responses: {
        Row: {
          created_at: string
          id: string
          response: string
          review_id: string
          updated_at: string
          vakman_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          response: string
          review_id: string
          updated_at?: string
          vakman_id: string
        }
        Update: {
          created_at?: string
          id?: string
          response?: string
          review_id?: string
          updated_at?: string
          vakman_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "review_responses_review_id_fkey"
            columns: ["review_id"]
            isOneToOne: true
            referencedRelation: "vakman_reviews"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "review_responses_vakman_id_fkey"
            columns: ["vakman_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      vakman_reviews: {
        Row: {
          comment: string | null
          created_at: string
          id: string
          job_id: string
          rating: number
          reviewer_id: string
          vakman_id: string
        }
        Insert: {
          comment?: string | null
          created_at?: string
          id?: string
          job_id: string
          rating: number
          reviewer_id: string
          vakman_id: string
        }
        Update: {
          comment?: string | null
          created_at?: string
          id?: string
          job_id?: string
          rating?: number
          reviewer_id?: string
          vakman_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "vakman_reviews_job_id_fkey"
            columns: ["job_id"]
            isOneToOne: false
            referencedRelation: "jobs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "vakman_reviews_reviewer_id_fkey"
            columns: ["reviewer_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "vakman_reviews_vakman_id_fkey"
            columns: ["vakman_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_users_needing_welcome_email: {
        Args: Record<PropertyKey, never>
        Returns: {
          user_id: string
          email: string
          first_name: string
          user_type: string
        }[]
      }
      handle_weekly_job_bonus: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      increment_balance: {
        Args: {
          user_id: string
          increment_amount: number
        }
        Returns: undefined
      }
    }
    Enums: {
      job_category_type:
        | "ventilation_cleaning"
        | "bathroom_renovation"
        | "general_maintenance"
        | "painting"
        | "electrical"
        | "plumbing"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

export interface Transaction {
  id: string;
  user_id: string;
  amount: number;
  type: 'deposit' | 'withdrawal';
  description: string | null;
  created_at: string;
  invoice_url: string | null;
  pdf_url: string | null;
  status: string | null;
  completed_at: string | null;
  mollie_payment_id: string | null;
}
