/**
 * @description This component renders a dedicated page for investment opportunities for professionals on the Klusgebied platform. It outlines the benefits of partnering with Klusgebied, such as business growth, access to a steady stream of jobs, and marketing support. The page is designed with a professional and trustworthy layout, featuring a compelling hero section, detailed benefit cards, and a clear call-to-action to encourage sign-ups. Key variables include the benefits data and navigation handlers.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Footer from "../../components/landing/Footer";
import usePageTitle from "../../hooks/usePageTitle";
import {
  ArrowRight,
  TrendingUp,
  Briefcase,
  BarChart,
  Users,
  ShieldCheck,
  Mail,
} from "lucide-react";

const InvesteringsmogelijkhedenPage = () => {
  usePageTitle("Investeringsmogelijkheden voor Vakmensen | Klusgebied");
  const navigate = useNavigate();

  const benefits = [
    {
      icon: TrendingUp,
      title: "Laat je Bedrijf G<PERSON>",
      description:
        "<PERSON>rijg toegang tot een constante stroom van gekwalificeerde klussen in jouw regio. Focus op je vak, wij zorgen voor de klanten.",
    },
    {
      icon: Briefcase,
      title: "Minder Administratie",
      description:
        "Ons platform stroomlijnt de communicatie, planning en facturatie, zodat jij meer tijd overhoudt voor je werk.",
    },
    {
      icon: BarChart,
      title: "Professionele Marketing",
      description:
        "Profiteer van onze landelijke marketingcampagnes en een professioneel profiel dat vertrouwen wekt bij potentiële klanten.",
    },
    {
      icon: Users,
      title: "Word Deel van een Netwerk",
      description:
        "Sluit je aan bij een community van geverifieerde vakmensen. Deel kennis, ervaring en werk samen aan grotere projecten.",
    },
  ];

  return (
    <div className="bg-white">
      <main>
        {/* Hero Section */}
        <section className="relative bg-slate-800 text-white overflow-hidden">
          <div className="absolute inset-0">
            <img
              src="https://images.unsplash.com/photo-1556761175-5973dc0f32e7?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=1920"
              alt="Professionals aan het werk"
              className="w-full h-full object-cover opacity-20"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-slate-900 via-slate-800/70 to-transparent"></div>
          </div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-32 pb-20 md:pt-40 md:pb-28 text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white motion-preset-fade-down">
              Investeer in je Toekomst als Vakman
            </h1>
            <p className="mt-6 max-w-3xl mx-auto text-lg md:text-xl text-slate-300 motion-preset-fade-down motion-delay-200">
              Sluit je aan bij Klusgebied en ontdek hoe ons platform jouw
              bedrijf kan laten groeien. Meer klussen, minder administratie en
              een professionele uitstraling.
            </p>
            <div className="mt-10 flex justify-center motion-preset-fade-down motion-delay-300">
              <button
                onClick={() => navigate("/vakman")}
                className="bg-teal-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/30 hover:-translate-y-1 inline-flex items-center"
              >
                <span>Meld je nu aan</span>
                <ArrowRight className="w-5 h-5 ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-20 lg:py-28 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-slate-800 mb-6">
                De Voordelen van een Partnerschap
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                Een samenwerking met Klusgebied is een investering in de groei
                en professionalisering van jouw bedrijf.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-shadow duration-300 flex items-start space-x-6"
                >
                  <div className="flex-shrink-0">
                    <div className="w-16 h-16 rounded-xl flex items-center justify-center bg-teal-100 text-teal-500">
                      <benefit.icon className="w-8 h-8" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-slate-800 mb-2">
                      {benefit.title}
                    </h3>
                    <p className="text-slate-600">{benefit.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* How it works Section */}
        <section className="py-20 lg:py-28 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-slate-800 mb-6">
                Hoe het Werkt
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                In drie eenvoudige stappen ben je klaar om klussen te ontvangen
                via Klusgebied.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div className="p-6">
                <div className="text-3xl font-bold text-teal-500 mb-4">1</div>
                <h3 className="text-xl font-bold text-slate-800 mb-2">
                  Meld je aan
                </h3>
                <p className="text-slate-600">
                  Maak een professioneel profiel aan en doorloop onze
                  verificatie.
                </p>
              </div>
              <div className="p-6">
                <div className="text-3xl font-bold text-teal-500 mb-4">2</div>
                <h3 className="text-xl font-bold text-slate-800 mb-2">
                  Ontvang Klussen
                </h3>
                <p className="text-slate-600">
                  Krijg meldingen van relevante klussen in jouw regio en
                  expertise.
                </p>
              </div>
              <div className="p-6">
                <div className="text-3xl font-bold text-teal-500 mb-4">3</div>
                <h3 className="text-xl font-bold text-slate-800 mb-2">
                  Voer uit & Groei
                </h3>
                <p className="text-slate-600">
                  Voer de klus uit, ontvang je betaling en bouw aan je
                  reputatie.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-slate-800 py-20">
          <div className="max-w-4xl mx-auto text-center px-4">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Klaar om te groeien?
            </h2>
            <p className="text-lg text-slate-300 mb-8">
              Word vandaag nog partner en ontdek de vele voordelen van het
              Klusgebied platform.
            </p>
            <button
              onClick={() => navigate("/vakman")}
              className="bg-teal-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/30 hover:-translate-y-1"
            >
              Start Vandaag Nog
            </button>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default InvesteringsmogelijkhedenPage;
