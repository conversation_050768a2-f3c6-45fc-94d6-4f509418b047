import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

import { supabase } from "@/integrations/supabase/client";
import { TableName, TableStats } from "./types";
import { useToast } from "@/hooks/use-toast";
import { ROUTE_PATHS } from "@/config/routes";

const TABLE_NAMES: TableName[] = [
  "profiles",
  "jobs",
  "job_responses",
  "contracts",
  "messages",
  "portfolio_projects",
  "vakman_reviews",
  "admin_logs",
  "admin_settings",
  "balance_transactions",
  "email_logs",
  "referrals",
  "review_responses",
];

const getTimestampField = (tableName: TableName): string => {
  if (tableName === "email_logs") return "sent_at";
  return "created_at";
};

export const useTableStats = () => {
  const [tableStats, setTableStats] = useState<Record<string, TableStats>>({});
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchTableStats = async () => {
      try {
        // Authentication check
        const {
          data: { session },
          error: sessionError,
        } = await supabase.auth.getSession();
        if (!session || sessionError) {
          throw new Error(sessionError?.message || "No active session");
        }

        // Admin verification
        const { data: profile, error: profileError } = await supabase
          .from("profiles")
          .select("user_type")
          .eq("id", session.user.id)
          .single();

        if (profileError || profile?.user_type !== "admin") {
          throw new Error("Unauthorized access");
        }

        // Fetch stats for all tables concurrently
        const stats: Record<string, TableStats> = {};
        await Promise.all(
          TABLE_NAMES.map(async (tableName) => {
            const timestampField = getTimestampField(tableName);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const weekAgo = new Date(today);
            weekAgo.setDate(weekAgo.getDate() - 7);

            // Parallel queries for each table
            const [
              { count: totalCount },
              { data: lastRecord },
              { count: todayCount },
              { count: weekCount },
            ] = await Promise.all([
              supabase
                .from(tableName)
                .select("*", { count: "exact", head: true }),
              supabase
                .from(tableName)
                .select(timestampField)
                .order(timestampField, { ascending: false })
                .limit(1),
              supabase
                .from(tableName)
                .select("*", { count: "exact", head: true })
                .gte(timestampField, today.toISOString()),
              supabase
                .from(tableName)
                .select("*", { count: "exact", head: true })
                .gte(timestampField, weekAgo.toISOString()),
            ]);

            stats[tableName] = {
              count: totalCount || 0,
              lastUpdated: lastRecord?.[0]?.[timestampField] || null,
              todayCount: todayCount || 0,
              weekCount: weekCount || 0,
            };
          })
        );

        setTableStats(stats);
      } catch (error) {
        console.error("Error:", error);
        toast({
          variant: "destructive",
          title:
            error instanceof Error && error.message === "Unauthorized access"
              ? "Geen toegang"
              : "Fout bij ophalen statistieken",
          description:
            error instanceof Error && error.message === "Unauthorized access"
              ? "Je hebt geen toegang tot deze pagina."
              : "Er is een fout opgetreden bij het ophalen van de database statistieken.",
        });
        navigate(
          error instanceof Error && error.message === "Unauthorized access"
            ? "/"
            : ROUTE_PATHS.AUTH
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchTableStats();
  }, [navigate, toast]);

  return { tableStats, isLoading, TABLE_NAMES };
};
